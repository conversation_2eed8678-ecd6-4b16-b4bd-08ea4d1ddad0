package com.checklist.debug;

import com.checklist.repository.ChecklistTemplateRepository;
import com.checklist.repository.ChecklistReviewRepository;
import com.checklist.model.ChecklistTemplate;
import com.checklist.model.ChecklistReview;

import java.io.IOException;
import java.util.List;

/**
 * 仓库路径调试工具
 */
public class RepositoryPathDebugger {
    
    public static void main(String[] args) {
        System.out.println("=== 仓库路径调试工具 ===\n");
        
        // 测试模板仓库
        System.out.println("1. 检查模板仓库:");
        ChecklistTemplateRepository templateRepo = new ChecklistTemplateRepository();
        System.out.println(templateRepo.getPathDebugInfo());
        
        try {
            List<ChecklistTemplate> templates = templateRepo.findAll();
            System.out.println("- 成功读取模板数量: " + templates.size());
            for (ChecklistTemplate template : templates) {
                System.out.println("  * " + template.getId() + " - " + template.getName());
            }
        } catch (IOException e) {
            System.err.println("- 读取模板失败: " + e.getMessage());
        }
        
        System.out.println("\n2. 检查评审仓库:");
        ChecklistReviewRepository reviewRepo = new ChecklistReviewRepository();
        System.out.println(reviewRepo.getPathDebugInfo());
        
        try {
            List<ChecklistReview> reviews = reviewRepo.findAll();
            System.out.println("- 成功读取评审数量: " + reviews.size());
            for (ChecklistReview review : reviews) {
                System.out.println("  * " + review.getId() + " - " + review.getTitle());
            }
        } catch (IOException e) {
            System.err.println("- 读取评审失败: " + e.getMessage());
        }
        
        System.out.println("\n=== 调试完成 ===");
    }
}
