import request, { type RequestConfig } from '@/utils/request'
import { type TableViewConfig } from '@/types/table-config'
import { type DefectRule, type StatusButtonGroup } from '@/types/defect-config'
import {
  type TemplateConfiguration,
  type SaveTemplateConfigRequest,
  type LoadTemplateConfigResponse
} from '@/types/template-config'

/**
 * 模板配置API服务
 * 提供统一的模板配置管理接口，以模板ID为维度管理所有配置
 */
class TemplateConfigApiService {
  private readonly baseUrl = '/template-config'

  /**
   * 加载模板的完整配置
   */
  async loadTemplateConfig(templateId: string, config?: RequestConfig): Promise<LoadTemplateConfigResponse> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/${templateId}`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * 保存模板的完整配置
   */
  async saveTemplateConfig(
    templateId: string,
    configData: SaveTemplateConfigRequest,
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    // 验证必填字段
    if (!configData.basicInfo.name?.trim()) {
      throw new Error('模板名称不能为空')
    }
    if (!configData.basicInfo.type?.trim()) {
      throw new Error('模板类型不能为空')
    }
    if (!configData.items || configData.items.length === 0) {
      throw new Error('检查项不能为空')
    }

    return request.put(`${this.baseUrl}/${templateId}`, configData, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板配置保存成功',
      ...config,
    })
  }

  /**
   * 获取模板的表头配置
   */
  async getTemplateTableConfig(templateId: string, config?: RequestConfig): Promise<TableViewConfig | null> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    try {
      return await request.get(`${this.baseUrl}/${templateId}/table-config`, {
        showLoading: false,
        ...config,
      })
    } catch (error) {
      // 如果没有配置，返回null
      return null
    }
  }

  /**
   * 保存模板的表头配置
   */
  async saveTemplateTableConfig(
    templateId: string,
    tableConfig: TableViewConfig,
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.put(`${this.baseUrl}/${templateId}/table-config`, tableConfig, {
      showLoading: true,
      showSuccess: true,
      successMessage: '表头配置保存成功',
      ...config,
    })
  }

  /**
   * 获取模板的缺陷规则配置
   */
  async getTemplateDefectRules(templateId: string, config?: RequestConfig): Promise<DefectRule[]> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    try {
      return await request.get(`${this.baseUrl}/${templateId}/defect-rules`, {
        showLoading: false,
        ...config,
      })
    } catch (error) {
      // 如果没有配置，返回空数组
      return []
    }
  }

  /**
   * 保存模板的缺陷规则配置
   */
  async saveTemplateDefectRules(
    templateId: string,
    defectRules: DefectRule[],
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.put(`${this.baseUrl}/${templateId}/defect-rules`, { rules: defectRules }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷规则保存成功',
      ...config,
    })
  }

  /**
   * 获取模板的状态按钮配置
   */
  async getTemplateStatusButtons(templateId: string, config?: RequestConfig): Promise<StatusButtonGroup[]> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    try {
      return await request.get(`${this.baseUrl}/${templateId}/status-buttons`, {
        showLoading: false,
        ...config,
      })
    } catch (error) {
      // 如果没有配置，返回空数组
      return []
    }
  }

  /**
   * 保存模板的状态按钮配置
   */
  async saveTemplateStatusButtons(
    templateId: string,
    statusButtons: StatusButtonGroup[],
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.put(`${this.baseUrl}/${templateId}/status-buttons`, { buttonGroups: statusButtons }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '状态按钮配置保存成功',
      ...config,
    })
  }

  /**
   * 删除模板的所有配置
   */
  async deleteTemplateConfig(templateId: string, config?: RequestConfig): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.delete(`${this.baseUrl}/${templateId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板配置删除成功',
      ...config,
    })
  }

  /**
   * 复制模板配置到新模板
   */
  async duplicateTemplateConfig(
    sourceTemplateId: string,
    targetTemplateId: string,
    config?: RequestConfig
  ): Promise<void> {
    if (!sourceTemplateId?.trim() || !targetTemplateId?.trim()) {
      throw new Error('源模板ID和目标模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/${sourceTemplateId}/duplicate`, {
      targetTemplateId
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板配置复制成功',
      ...config,
    })
  }
}

// 创建服务实例
const templateConfigApi = new TemplateConfigApiService()

// 导出方法
export const loadTemplateConfig = templateConfigApi.loadTemplateConfig.bind(templateConfigApi)
export const saveTemplateConfig = templateConfigApi.saveTemplateConfig.bind(templateConfigApi)
export const getTemplateTableConfig = templateConfigApi.getTemplateTableConfig.bind(templateConfigApi)
export const saveTemplateTableConfig = templateConfigApi.saveTemplateTableConfig.bind(templateConfigApi)
export const getTemplateDefectRules = templateConfigApi.getTemplateDefectRules.bind(templateConfigApi)
export const saveTemplateDefectRules = templateConfigApi.saveTemplateDefectRules.bind(templateConfigApi)
export const getTemplateStatusButtons = templateConfigApi.getTemplateStatusButtons.bind(templateConfigApi)
export const saveTemplateStatusButtons = templateConfigApi.saveTemplateStatusButtons.bind(templateConfigApi)
export const deleteTemplateConfig = templateConfigApi.deleteTemplateConfig.bind(templateConfigApi)
export const duplicateTemplateConfig = templateConfigApi.duplicateTemplateConfig.bind(templateConfigApi)

// 导出服务实例
export default templateConfigApi
